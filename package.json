{"name": "mcp-gemini-server", "version": "0.1.0", "description": "My new MCP Server", "main": "dist/server.js", "bin": {"mcp-gemini-server": "dist/initialize.js"}, "type": "module", "scripts": {"start": "node dist/server.js", "build": "tsc", "dev": "nodemon --watch src --ext ts --exec \"node --loader ts-node/esm src/server.ts\"", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "test": "echo \"Error: no test specified\" && exit 1", "prepare": "husky install || true"}, "keywords": ["mcp", "model-context-protocol"], "license": "ISC", "dependencies": {"@google/genai": "^0.7.0", "@modelcontextprotocol/sdk": "^1.7.0", "@types/inquirer": "^9.0.7", "@types/uuid": "^10.0.0", "ajv": "^8.0.0", "ajv-formats": "^3.0.1", "chalk": "^5.3.0", "inquirer": "^12.5.0", "uuid": "^11.1.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.2", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "lint-staged": "^15.2.5", "nodemon": "^3.1.3", "prettier": "^3.3.2", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"]}}