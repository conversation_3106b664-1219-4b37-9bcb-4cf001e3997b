// Fix the Ajv import and instantiation
import Ajv from "ajv";

// Initialize Ajv with formats
// Use specific options to ensure compatibility
const ajv = new Ajv({
  strict: false,
  allErrors: true
});

// Import and apply formats to the Ajv instance
import addFormats from "ajv-formats";
// @ts-ignore
addFormats(ajv);

// Your existing schema definition
const schema = {
  type: "object",
  properties: {
    sessionId: {
      type: "string",
      pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
    },
    // other properties...
  }
};
