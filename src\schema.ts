// Fix the Ajv import and instantiation for ES modules
import Ajv from "ajv";
import addFormats from "ajv-formats";

// Initialize Ajv with formats
// Use specific options to ensure compatibility
const ajv = new (Ajv as any)({
  strict: false,
  allErrors: true
});

// Apply formats to the Ajv instance
(addFormats as any)(ajv);

// Your existing schema definition
const schema = {
  type: "object",
  properties: {
    sessionId: {
      type: "string",
      pattern: "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"
    },
    // other properties...
  }
};
