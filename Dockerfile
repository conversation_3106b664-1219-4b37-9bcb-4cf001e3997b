# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM node:lts-alpine

WORKDIR /app

# Copy package files and install dependencies
COPY package.json package-lock.json ./

# Install dependencies without running lifecycle scripts
RUN npm ci --ignore-scripts

# Copy the rest of the app
COPY . .

# Build the TypeScript code
RUN npm run build

# Expose port if needed (optional)
# EXPOSE 3000

# Start the server
CMD ["node", "dist/server.js"]
