﻿{
  "compilerOptions": {
    "target": "ES2022", /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */
    "module": "NodeNext", /* Specify what module code is generated. */
    "moduleResolution": "NodeNext", /* Specify how TypeScript looks up a file from a given module specifier. */
    "outDir": "./dist", /* Specify an output folder for all emitted files. */
    "rootDir": "./src", /* Specify the root folder within your source files. */
    "esModuleInterop": true, /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables 'allowSyntheticDefaultImports' for type compatibility. */
    "forceConsistentCasingInFileNames": true, /* Ensure that casing is correct in imports. */
    "strict": true, /* Enable all strict type-checking options. */
    "skipLibCheck": true, /* Skip type checking all .d.ts files. */
    "resolveJsonModule": true, /* Enable importing .json files */
    "sourceMap": true, /* Create source map files for emitted JavaScript files. */
    "declaration": true, /* Generate .d.ts files from TypeScript and JavaScript files in your project. */
    "declarationMap": true, /* Create sourcemaps for d.ts files. */
    "allowJs": true, /* Allow JavaScript files to be a part of your program. */
  },
  "ts-node": { /* ts-node specific options */
    "transpileOnly": true, /* Skip type checking for faster execution */
    "files": true /* Include files in tsconfig.json */
  },
  "include": [
    "src/**/*"
  ], /* Specifies an array of filenames or patterns to include in the program */
  "exclude": [
    "node_modules",
    "dist"
  ] /* Specifies an array of filenames or patterns that should be skipped when resolving include */
}